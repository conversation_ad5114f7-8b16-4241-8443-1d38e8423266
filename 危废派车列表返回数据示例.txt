{"actions": [{"action": "export", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-share-alt", "id": "01564abb5fd08a8ae61a564409bc074a", "key": "export", "legend": "", "list": "", "method": "POST", "name": "导出", "primary": false, "rule": "", "selected": "none", "view": ""}, {"action": "", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"finish\",\"contact\"]},\"then\":{\"bpm_status\":\"contact\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-phone-alt", "id": "01543747a7398a8ae61a538908b40ca0", "key": "", "legend": "", "list": "waste_id,package_type,security_measure,outer_goal,handle_type,five_bills_code,produce_enterprise,transfer_enterprise,dispose_enterprise,transfer_person,car_id,card_id,lock_id,transfer_start_position,transfer_end_position,is_send_info", "method": "PUT", "name": "联系外运", "primary": true, "rule": "", "selected": "single", "view": ""}, {"action": "", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-inbox", "id": "********************************", "key": "recontact", "legend": "再次派车", "list": "five_bills_code,produce_enterprise,transfer_enterprise,dispose_enterprise,transfer_person,car_id,card_id,lock_id,transfer_start_position,transfer_end_position,is_send_info", "method": "PUT", "name": "再次派车", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-change-transfer", "actionParam": "", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-random", "id": "0156d9eca4338a8ae61a56d6183402e0", "key": "change", "legend": "", "list": "", "method": "POST", "name": "固废明细转单", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"contact\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-road", "id": "015726c016848a8ae61a57225de50251", "key": "send-to-lms", "legend": "", "list": "", "method": "POST", "name": "发送派车信息", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"out-store\",\"weight\",\"print\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-import", "id": "015591151ef98a8ae61a558c392b0343", "key": "weight", "legend": "", "list": "weight_record_code,weight,is_send_info,five_bills_code", "method": "PUT", "name": "过磅", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-file", "id": "01545655a2548a8ae61a538908b42029", "key": "print", "legend": "", "list": "", "method": "POST", "name": "联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order-out", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-file", "id": "01555d33c84c8a8ae61a555d0e3b002b", "key": "print-n-out", "legend": "", "list": "", "method": "POST", "name": "宁波省外联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "e.hwms-contacts-order-in", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"weight\",\"print\"]},\"then\":{\"bpm_status\":\"print\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-file", "id": "01555d35c97e8a8ae61a555d0e3b0033", "key": "print-n-in", "legend": "", "list": "", "method": "POST", "name": "宁波省内联单打印", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "file", "actionParam": "{\"before\":{\"and\":{\"bpm_status\":[\"print\"]}},\"update\":{\"if\":{\"bpm_status\":[\"print\"]},\"then\":{\"bpm_status\":\"upload\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-plus", "id": "015566f380048a8ae61a555d0e3b0a2a", "key": "upload", "legend": "", "list": "", "method": "POST", "name": "扫描件上传", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"update\":{\"if\":{\"bpm_status\":[\"finish\",\"upload\"]},\"then\":{\"bpm_status\":\"close\"}}}", "allowMobile": false, "controls": "", "display": "", "group": "1", "i18nCode": "", "icon": "glyphicon-remove-sign", "id": "01559ac7f7408a8ae61a559ab4a80039", "key": "close", "legend": "", "list": "", "method": "POST", "name": "关闭", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"weight\",\"print\",\"upload\",\"close\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-refresh", "id": "01571d687aa88a8ae61a56fcd1e30c07", "key": "picture", "legend": "", "list": "", "method": "POST", "name": "获取过磅图片", "primary": false, "rule": "", "selected": "single", "view": ""}, {"action": "one", "actionParam": "{\"before\":{\"and\":{\".bpm_status\":[\"contact\",\"out-store\",\"weight\",\"print\",\"upload\",\"close\"]}}}", "allowMobile": false, "controls": "", "display": "", "group": "", "i18nCode": "", "icon": "glyphicon-remove", "id": "0159af7a51b88a8afbb85972e76701dd", "key": "clear-pls-info", "legend": "该操作会清空物流系统对应单号的派车信息，需地磅系统配合处理", "list": "", "method": "POST", "name": "清空派车信息", "primary": false, "rule": "", "selected": "single", "view": ""}], "data": {"collection": [{"transfer_id": "019034505bab8a28fc979031208123d3", "apply_code": "202406200002", "user_name": "杜林涛", "org_id": "**********", "org_name": "生产IT", "parent_org_id": "**********", "parent_org_name": "信息中心", "hwms_org_id": "0175aa6f708b8a2808557587d3d80ec8", "company_id": "**********", "apply_date": 1718812800000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "13333333333", "plan_transfer_quantity": "2", "plan_transfer_time": 1718899200000, "transfer_time": 1718865458413, "duty_person": "李四", "operator": "张安民", "transfer_position": "存储点", "waste_id": "015846d4a4fb8a8ae704583e04c30d93", "waste_name": "粘有物料的废金属管线", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "粘有物料的废金 属", "risk": "毒性,易燃性,反应性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "箱装", "security_measure": "防火、防爆、防漏", "outer_goal": "处理", "handle_type": "3", "produce_enterprise": "01568bdd7ddf8a8ae704568883a801a6", "transfer_enterprise": "01786d2693c08a28fc83786cfbf80167", "dispose_enterprise": "0159724041f18a8ae703596dfa350726", "five_bills_code": "WLD20240620002", "transfer_person": "承运12345", "car_id": "015549177c828a8ae61a5548ac3700a3", "card_id": "cc8342ed", "card_code": "cc8342ed", "card_name": "4413车卡", "lock_id": "", "transfer_start_position": "起点", "transfer_end_position": "终点", "weight_record_code": "202406200001", "weight": 100, "note": "测试", "wait_count": "0", "pass_count": "2", "back_count": "0", "bpm_status": "print", "bpm_update_time": 1718865213899, "update_time": 1718865546824, "is_send_info": "1", "year": "2024", "division_id": "**********"}, {"transfer_id": "01902f1833758a28fcab902931bb4283", "apply_code": "202406190001", "user_name": "杜林涛", "org_id": "**********", "org_name": "生产IT", "parent_org_id": "**********", "parent_org_name": "信息中心", "hwms_org_id": "0175aa6f708b8a2808557587d3d80ec8", "company_id": "**********", "apply_date": 1718726400000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "13333333333", "plan_transfer_quantity": "2", "plan_transfer_time": 1718812800000, "transfer_time": 1718780466822, "duty_person": "张三", "operator": "张安民", "transfer_position": "测试位置", "waste_id": "015846d4a4fb8a8ae704583e04c30d93", "waste_name": "粘有物料的废金属管线", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "粘有物料的废金 属", "risk": "毒性,易燃性,反应性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "袋装", "security_measure": "防火、防爆、防漏", "outer_goal": "处置", "handle_type": "3", "produce_enterprise": "01568bdd7ddf8a8ae704568883a801a6", "transfer_enterprise": "01786d2693c08a28fc83786cfbf80167", "dispose_enterprise": "0159724041f18a8ae703596dfa350726", "five_bills_code": "WLD123456", "transfer_person": "承运123", "car_id": "01552977cf6b8a8a8b1f55296e6e001c", "card_id": "cc8342ed", "card_code": "cc8342ed", "card_name": "4413车卡", "transfer_start_position": "起点", "transfer_end_position": "终点", "weight_record_code": "GBD20240619001", "weight": 100, "note": "备注", "wait_count": "1", "pass_count": "1", "back_count": "0", "bpm_status": "print", "bpm_update_time": 1718779616122, "update_time": 1718780598037, "is_send_info": "1", "year": "2024", "division_id": "**********"}, {"transfer_id": "018ffbc35df28a28fbd48fe74b89618c", "apply_code": "202406090001", "user_name": "杜林涛", "org_id": "**********", "org_name": "生产IT", "parent_org_id": "**********", "parent_org_name": "信息中心", "hwms_org_id": "0175aa6f708b8a2808557587d3d80ec8", "company_id": "**********", "apply_date": 1717862400000, "apply_type": "equ", "is_plan": "1", "transfer_type": "outer", "is_sales": "0", "phone": "0535-5488663", "plan_transfer_quantity": "3", "plan_transfer_time": 1717948800000, "transfer_time": 1717916459815, "duty_person": "转移联系人", "operator": "jjj", "transfer_position": "2143", "waste_id": "015846d4a4fb8a8ae704583e04c30d93", "waste_name": "粘有物料的废金属管线", "category_code": "900-041-49", "parent_category_code": "HW49", "report_group_name": "粘有物料的废金 属", "risk": "毒性,易燃性,反应性", "waste_modal": "固态", "harmful_ingredient": "", "package_type": "桶装", "security_measure": "防火、防爆、防漏", "outer_goal": "处置", "handle_type": "3", "produce_enterprise": "01568bdd7ddf8a8ae704568883a801a6", "transfer_enterprise": "01786d2693c08a28fc83786cfbf80167", "dispose_enterprise": "015549120b7d8a8ae61a5548ac370099", "five_bills_code": "TEST2345WQ", "transfer_person": "承运人123", "car_id": "015549186daa8a8ae61a5548ac3700a5", "card_id": "cc8342ed", "card_code": "cc8342ed", "card_name": "4413车卡", "transfer_start_position": "起点", "transfer_end_position": "终点", "weight_record_code": "32514515eryu", "weight": 100, "note": "备注", "wait_count": "0", "pass_count": "2", "back_count": "0", "bpm_status": "print", "bpm_update_time": 1717916185424, "update_time": 1717916791336, "is_send_info": "1", "year": "2024", "division_id": "**********"}, {"transfer_id": "0178fdd7c28a8a28f20578fdd0aa002f", "apply_code": "202104230006", "user_name": "郭敬彬", "org_id": "**********", "org_name": "MDI装置", "parent_org_id": "**********", "parent_org_name": "MDI装置", "hwms_org_id": "**********", "company_id": "**********", "apply_date": 1619107200000, "apply_type": "equ", "is_plan": "0", "transfer_type": "outer", "is_sales": "0", "phone": "0535-8202935", "plan_transfer_quantity": "4", "plan_transfer_time": 1619107200000, "transfer_time": 1619166933093, "duty_person": "朱学亮", "transfer_position": "1339光化", "waste_id": "015673943b738a8ae7045667b16d0999", "waste_name": "有机废液", "category_code": "900-404-06", "parent_category_code": "HW06", "report_group_name": "有机废液", "risk": "毒性,易燃性", "waste_modal": "液态", "harmful_ingredient": "", "package_type": "桶装", "security_measure": "防火、防爆、防漏", "outer_goal": "处置", "handle_type": "3", "produce_enterprise": "01568bdd7ddf8a8ae704568883a801a6", "transfer_enterprise": "01786d2693c08a28fc83786cfbf80167", "dispose_enterprise": "0159a5ee7aa78a8ae704596df98971ed", "five_bills_code": "20213706K0014332", "transfer_person": "朱学亮", "car_id": "015549177c828a8ae61a5548ac3700a3", "card_id": "1F80A755", "card_code": "二期车卡2", "card_name": "二期车卡2", "transfer_start_position": "万华", "transfer_end_position": "光大", "weight_record_code": "11111", "weight": 111111, "note": "1111", "wait_count": "0", "pass_count": "2", "back_count": "0", "bpm_status": "print", "bpm_update_time": 1619166744042, "update_time": 1619167057990, "is_send_info": "1", "year": "2021", "division_id": "**********"}], "count": 4, "exists": false, "pagination": {"count": 1, "current": 1, "paging": true, "size": 15}}, "extend": "", "handle": "mulpitle", "icon": "", "id": "01543747a6c88a8ae61a538908b40c86", "layout": "", "legend": "视图", "mode": "table", "name": "固废外运处置（全部）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "转移主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "transfer_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请单号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "基础信息", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "apply_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "user_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "工序", "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "申请单工序", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"_childList": [{"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "生产IT", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "信息中心", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "万华化学烟台生产基地", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "万华化学集团股份有限公司", "org_stitle": "", "parent_org_id": "", "status": ""}], "count": 5, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "**********", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "工序", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "装置", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "申请单装置", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"_childList": [{"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "生产IT", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "信息中心", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "万华化学烟台生产基地", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "万华化学集团股份有限公司", "org_stitle": "", "parent_org_id": "", "status": ""}], "count": 5, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "装置", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "parent_org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "组织", "column": 6, "control": "view-select", "dataType": "wordbook", "dictMode": "bydata", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "hwms_org_id", "params": {"control": {"displayValue": "org_name", "realValue": "org_id", "url": "/hwms/api/hwms-org-select"}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_id": "0175aa6f708b8a2808557587d3d80ec8", "org_name": "信息中1", "parent_org_id": "**********"}, {"org_alias": "", "org_id": "**********", "org_name": "MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "count": 2, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "01626be7b4838a8afbb86266191b01e8", "layout": "", "legend": "", "mode": "tree", "name": "HWMS虚拟组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "control": "", "dataType": "uuid", "group": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "control": "text", "dataType": "string", "display": "title", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "control": "", "dataType": "wordbook", "display": "", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "control": "text", "dataType": "integer", "group": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "control": "select", "dataType": "string", "display": "", "events": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "所属公司", "column": 6, "control": "select", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "company_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"org_id": "**********", "org_name": "万华化学集团股份有限公司"}], "count": 1, "exists": false}, "extend": "", "handle": "mulpitle", "icon": "", "id": "017862d0a8948a28483d7862af7f002a", "layout": "", "legend": "HWMS查询类型为公司的组织机构", "mode": "table", "name": "HWMS公司字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "**********", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请日期", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "apply_date", "remind": "", "required": true, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "1756310400000", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "申请类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "固废转移", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "equ:装置,station:固废站", "maxLength": 64, "metaType": "String", "name": "apply_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "是否计划内", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_plan", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "inner:转移至固废站,outer:外运", "maxLength": 64, "metaType": "String", "name": "transfer_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "内部转移", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "处置去向", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "0:直接外委处置,1:有价值处置,2:环保科技处置", "maxLength": 64, "metaType": "String", "name": "is_sales", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "0", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "联系电话", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "phone", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "预计转移量", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "plan_transfer_quantity", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "预计转移时间", "control": "datetime", "dataType": "date", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "plan_transfer_time", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "实际转移时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "transfer_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移联系人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "duty_person", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "实际操作人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "operator", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "废物产生位置", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废名称", "column": 6, "control": "view-select", "dataType": "wordbook", "dictMode": "bydata", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "waste_id", "params": {"control": {"url": "/hwms/api/chosen-waste-name-all"}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [{"group": "1", "icon": "glyphicon-plus", "id": "01783f6cb81f8a28a07d783f3926005d", "key": "", "method": "POST", "name": "新增", "primary": true, "selected": "none"}, {"group": "1", "icon": "glyphicon-pencil", "id": "01783f6cb8218a28a07d783f3926005e", "key": "", "method": "PUT", "name": "修改", "primary": false, "selected": "single"}, {"group": "", "icon": "glyphicon-trash", "id": "01783f6cb8238a28a07d783f3926005f", "key": "", "method": "DELETE", "name": "删除", "primary": false, "selected": "somewhat"}], "data": {"collection": [{"category_id": "0156726c3e508a8ae7045667b16d04cf", "is_key_waste": "", "report_group_id": "0156738509fd8a8ae7045667b16d0942", "status": "1", "waste_id": "015846d4a4fb8a8ae704583e04c30d93", "waste_name": "粘有物料的废金属管线", "waste_source": ""}, {"category_id": "0156724d4a708a8ae7045667b16d0495", "is_key_waste": "", "report_group_id": "01567300fa9a8a8ae7045667b16d0738", "sort_num": 3, "status": "1", "waste_id": "015673943b738a8ae7045667b16d0999", "waste_name": "有机废液", "waste_source": ""}], "count": 2, "exists": false}, "extend": "", "handle": "mulpitle", "icon": "", "id": "01783f6cb7b68a28a07d783f39260053", "layout": "", "legend": "视图", "mode": "table", "name": "固废名称视图选择器（所有）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "固废主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "waste_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废分类", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "category_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废分组", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "report_group_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "固废来源", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_source", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "是否重点关注固废", "column": 6, "control": "select", "dataType": "string", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_key_waste", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "1:有效,0:无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "序号", "control": "text", "dataType": "integer", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [{"alias": "固废名称", "control": "text", "name": "waste_name", "required": false, "value": ""}, {"alias": "固废分类", "control": "select", "name": "category_id", "required": false, "value": ""}, {"alias": "固废分组", "control": "select", "name": "report_group_id", "required": false, "value": ""}], "idName": "waste_id", "nodeIdName": "waste_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "固废名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "固废信息", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废代码", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "category_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废类别", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "parent_category_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "报批分组", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "report_group_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "危险特性", "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "毒性,腐蚀性,易燃性,反应性,感染性", "maxLength": 64, "metaType": "String", "name": "risk", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "固废形态", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "waste_modal", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "主要有害成分", "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "harmful_ingredient", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "包装方式", "column": 6, "control": "multiple", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "袋装,桶装,箱装,空桶,槽罐,管道运输,其它", "maxLength": 64, "metaType": "String", "name": "package_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "禁忌与应急措施", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "security_measure", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "防火、防爆、防漏", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "外运目的", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "外运信息", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "中转贮存,利用,处理,处置,其他", "maxLength": 64, "metaType": "String", "name": "outer_goal", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "处置", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "废物处置方式", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "20:D1-填埋,21:D9-物理化学处理,22:D10-焚烧,23:R2-溶剂回收/再生（如蒸馏、萃取等）,24:R3-再循环/再利用不是用作溶剂的有机物,25:R4-再循环/再利用金属和金属化合物,26:R5-再循环/再利用其他无机物,27:R6-再生酸或碱,28:R7-回收污染减除剂的组分,29:R8-回收催化剂组分,30:R9-废油再提炼或其他废油的再利用,31:R15-其他,32:C1-水泥窑共处置,33:C5-收集废物", "maxLength": 64, "metaType": "String", "name": "handle_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "产生企业", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "produce_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "01568bdd7ddf8a8ae704568883a801a6", "enterprise_name": "万华化学集团股份有限公司", "enterprise_property": "国有", "enterprise_type": "produce", "industry_code": "C2619"}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd439080e8a28906d6bcf6b81001f", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（产生企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输企业", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "transfer_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "01786d2693c08a28fc83786cfbf80167", "enterprise_name": "潍坊佳鹏物流有限公司", "enterprise_property": "私企", "enterprise_type": "transfer", "industry_code": "913707037871610347"}], "count": 1, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd4394bd88a28906d6bcf6b810031", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（运输企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "接受单位", "column": 6, "control": "chosen", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "dispose_enterprise", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"enterprise_id": "015549120b7d8a8ae61a5548ac370099", "enterprise_name": "鑫广绿环再生资源股份有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": ""}, {"enterprise_id": "0159724041f18a8ae703596dfa350726", "enterprise_name": "青岛阳林鸿化工有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": "无"}, {"enterprise_id": "0159a5ee7aa78a8ae704596df98971ed", "enterprise_name": "光大环保危废处置（淄博）有限公司", "enterprise_property": "私企", "enterprise_type": "dispose", "industry_code": ""}], "count": 3, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "016bd4391e7e8a28906d6bcf6b810028", "layout": "", "legend": "视图", "mode": "table", "name": "企业字典（处置企业）", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "企业主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "enterprise_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "enterprise_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业性质", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "国有,私企", "maxLength": 64, "metaType": "String", "name": "enterprise_property", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "行业代码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "industry_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "企业类型", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "produce:产生企业,dispose:处置企业,transfer:运输企业", "maxLength": 64, "metaType": "String", "name": "enterprise_type", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "enterprise_id", "nodeIdName": "enterprise_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "五联单号", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "山东省内转移无需填写", "list": "", "maxLength": 64, "metaType": "String", "name": "five_bills_code", "params": {"control": {"events": {"change:dispose_enterprise": "hide|01783ddecaea8a87d89c783ddab70005", "change:produce_enterprise": "hide|01783ddecaea8a87d89c783ddab70005"}}}, "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "承运人", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_person", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "废物运输车牌号", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"car_code": "鲁F68601", "car_id": "01552977cf6b8a8a8b1f55296e6e001c", "car_type": "重型普通货车", "note": "", "region_id": "015572306a778a8ae61a5570bbef10da", "status": "有效", "transfer_code": "370601700133", "valid_date": 1483977600000}, {"car_code": "鲁F68689", "car_id": "015549177c828a8ae61a5548ac3700a3", "car_type": "重型普通货车", "note": "", "region_id": "015572306a778a8ae61a5570bbef10da", "status": "有效", "transfer_code": "370601700132", "valid_date": 1482854400000}, {"car_code": "鲁F62727", "car_id": "015549186daa8a8ae61a5548ac3700a5", "car_type": "重型仓栅式货车", "note": "", "region_id": "015572306a778a8ae61a5570bbef10da", "status": "有效", "transfer_code": "************", "valid_date": 1477497600000}], "count": 3, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0154370cf0088a8ae61a538908b40bfe", "layout": "", "legend": "视图", "mode": "table", "name": "车辆字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "车辆主键", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "car_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车牌号", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "car_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车辆类型", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "槽罐车,中型卡车,大型卡车,特级车辆,重型厢式货车,重型普通货车,重型仓栅式货车,重型半挂牵引车,管道输送", "maxLength": 64, "metaType": "String", "name": "car_type", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输证号", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_code", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "有效期", "control": "date", "dataType": "date", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "valid_date", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "hidden": "list", "i18nCode": "", "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "select", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "有效,无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "有效", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "区域主键", "column": 6, "control": "", "dataType": "id", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "region_id", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "015572306a778a8ae61a5570bbef10da", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "car_id", "nodeIdName": "car_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "车卡", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"card_code": "二期车卡2", "card_id": "1F80A755", "card_name": "二期车卡2", "card_object": "", "card_status": "N", "card_type": "car"}], "count": 1, "exists": false}, "extend": "", "handle": "", "icon": "", "id": "0155b4e445ce8a8ae61a55b07772058e", "layout": "", "legend": "IC卡信息视图", "mode": "table", "name": "可用车卡字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "主键", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "<PERSON><PERSON>ly", "alias": "物理编号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "卡类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "car:车卡,person:员工卡", "maxLength": 64, "metaType": "String", "name": "card_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "有效期", "control": "date", "dataType": "date", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "card_validity", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "绑定对象", "control": "text", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "card_object", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "卡状态", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "Y:是,N:否", "maxLength": 64, "metaType": "String", "name": "card_status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "N", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "card_id", "nodeIdName": "card_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "车卡", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "车卡", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "card_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "电子锁", "control": "chosen", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "lock_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": []}, "extend": "", "handle": "", "icon": "", "id": "0155b4e6a4c78a8ae61a55b0777205a2", "layout": "", "legend": "视图", "mode": "table", "name": "可用电子锁字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "主键", "control": "", "dataType": "uuid", "hidden": "all", "i18nCode": "", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "device_id", "remind": "", "required": true, "system": "none", "type": "value", "unique": true, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备类型", "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "handset:手持机,scale:磅秤,lock:电子锁", "maxLength": 64, "metaType": "String", "name": "device_type", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备名称", "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备编码", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_code", "remind": "", "required": true, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "设备标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "device_identifies", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "MAC", "control": "", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "mac", "remind": "FF:FF:FF:FF:FF:FF", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "状态", "control": "chosen", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "valid:有效,invalid:无效", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "valid", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "text", "dataType": "string", "hidden": "", "i18nCode": "", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "device_id", "nodeIdName": "device_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "电子锁", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "lock_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "电子锁回收确认时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "lock_recycle_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输起点", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_start_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "运输终点", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "transfer_end_position", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "过磅单号", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "weight_record_code", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "皮重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "tare_weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "毛重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "gross_weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "净重(KG)", "control": "text", "dataType": "number", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Number", "name": "weight", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "first_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅点标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "first_node", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "空车过磅点名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "first_node_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅时间", "control": "datetime", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "second_time", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅点标识", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "second_node", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "重车过磅点名称", "control": "text", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "second_node_name", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "备注", "control": "textarea", "dataType": "text", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 655350, "metaType": "Text", "name": "note", "remind": "", "required": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "待转移总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "wait_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "通过总数", "control": "", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "pass_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "write", "alias": "退回总数", "control": "text", "dataType": "integer", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "back_count", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "function(value, row){\r\n\tvar style = 'style=\"color:red;font-weight:bold;\" ';    \r\n    var bpmStatus = row['bpm_status'];\r\n    if(bpmStatus == \"weight\" || bpmStatus == \"print\" || bpmStatus == \"upload\") {\r\n\t    var transferTime = row['transfer_time']; \r\n\t    style = ((typeof(transferTime) == \"undefined\") || (transferTime == ''))\r\n        ? style : '';\r\n\t    return '<span ' +style+ '>'+value+'</span>';\r\n\t} else {\r\n\t\treturn value;\r\n\t}    \r\n}", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "finish:审批完成,contact:联系外运,out-store:已出库,weight:已过磅,print:联单已打印,upload:联单已上传", "maxLength": 64, "metaType": "String", "name": "bpm_status", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "draft", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "BPM更新时间", "column": 6, "control": "text", "dataType": "time", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "BPM更新时间", "list": "", "maxLength": 40, "metaType": "Time", "name": "bpm_update_time", "remind": "", "required": false, "sort": false, "system": "write", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "更新时间", "control": "", "dataType": "time", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 40, "metaType": "Time", "name": "update_time", "remind": "", "required": false, "sort": false, "system": "write", "type": "value", "unique": false, "value": "1756346843073", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "五联单扫描件", "column": 12, "control": "file", "dataType": "file", "dictMode": "", "display": "", "events": "", "format": "function(value, row){\n  if (row.file !== undefined && row.file !== null && row.file != '') {\n    var url = 'hwms/api/hwit-paper/'  + row.transfer_id + '?_mode=attachment&bust=' + (new Date()).getTime();\n    return '联单附件：<a href=\"' + url +  '\" target=\"_blank\">点击下载</a>';\n  } else {\n    return '未上传';\n  }\n}", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "file", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "主单据", "column": 6, "control": "text", "dataType": "id", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "master_transfer_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "转移明细", "control": "", "dataType": "hwms_transfer_detail", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 512, "metaType": "model", "name": "detail", "remind": "", "required": false, "sort": false, "system": "none", "type": "collection", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "出门证", "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "out_paper", "remind": "", "required": false, "sort": false, "system": "none", "type": "link", "unique": false, "value": "https://frqas.whchem.com:8043/webroot/decision/view/report?viewlet=whchem/it/ppit/prj/hwms/A1出门证.cpt&transferId=<%=transfer_id%>&__bypagesize__=false", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "出门证（new）", "column": 6, "control": "", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "out_paper_new", "remind": "", "required": false, "sort": false, "system": "none", "type": "link", "unique": false, "value": "https://frqas.whchem.com:8043/webroot/decision/view/report?viewlet=whchem/it/ppit/prj/hwms/A1出门证.cpt&transferId=<%=transfer_id%>", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "发送派车信息", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "用于系统判断是否发送派车信息给LMS", "list": "1:是,0:否", "maxLength": 64, "metaType": "String", "name": "is_send_info", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "1", "verifyRule": "", "virtual": "true"}, {"access": "readonly", "alias": "年份", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "year", "remind": "", "required": false, "sort": false, "system": "<PERSON><PERSON>ly", "type": "value", "unique": false, "value": "2025", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "相关附件", "control": "", "dataType": "hwms_file", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "list", "i18nCode": "", "ignore": false, "legend": "转移单附件——主要为过磅图片", "list": "", "maxLength": 512, "metaType": "model", "name": "attachment", "remind": "", "required": false, "sort": false, "system": "none", "type": "collection", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "所属事业部", "column": 6, "control": "select", "dataType": "wordbook", "dictMode": "all", "display": "", "events": "", "format": "", "group": "", "hidden": "all", "i18nCode": "", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "division_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "typeObject": {"actions": [], "data": {"collection": [{"_childList": [{"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "生产IT", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "信息中心", "org_stitle": "", "parent_org_id": "**********", "status": ""}, {"_childList": [{"org_alias": "", "org_id": "**********", "org_name": "MDI装置", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "万华化学烟台生产基地", "org_stitle": "", "parent_org_id": "**********", "status": ""}], "org_alias": "", "org_id": "**********", "org_name": "万华化学集团股份有限公司", "org_stitle": "", "parent_org_id": "", "status": ""}], "count": 5, "exists": false}, "extend": "", "handle": "single", "icon": "", "id": "014cfe019cdaf9458a5d4cfdb2d00276", "layout": "", "legend": "", "mode": "tree", "name": "组织机构字典", "schema": {"async": false, "attributes": [{"access": "readonly", "alias": "组织主键", "column": 6, "control": "", "dataType": "uuid", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_id", "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "org_id", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织名称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "title", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_org_name", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_name", "remind": "", "required": true, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织别名", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_alias", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_alias", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织简称", "column": 6, "control": "text", "dataType": "string", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_abbreviation", "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "org_stitle", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "父组织", "column": 6, "control": "", "dataType": "wordbook", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_parent_org", "ignore": false, "legend": "", "list": "", "maxLength": 48, "metaType": "Key", "name": "parent_org_id", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "write", "alias": "组织排序", "column": 6, "control": "text", "dataType": "integer", "dictMode": "", "display": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_sort_num", "legend": "", "list": "", "maxLength": 20, "metaType": "Integer", "name": "sort_num", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}, {"access": "readonly", "alias": "数据状态", "column": 6, "control": "select", "dataType": "string", "dictMode": "", "display": "", "events": "", "format": "", "group": "", "hidden": "", "i18nCode": "develop_status", "ignore": false, "legend": "", "list": "", "maxLength": 64, "metaType": "String", "name": "status", "remind": "", "required": false, "sort": false, "system": "none", "type": "value", "unique": false, "value": "", "verifyRule": "", "virtual": "false"}], "filters": [], "idName": "org_id", "nodeIdName": "org_id", "parentIdName": "parent_org_id"}, "setting": "", "template": "", "type": "standard"}, "unique": false, "value": "**********", "verifyRule": "", "virtual": "false"}], "filters": [{"alias": "状态", "control": "select", "isRange": false, "name": "bpm_status", "required": false, "value": ""}, {"alias": "申请单号", "control": "text", "isRange": false, "name": "apply_code", "required": false, "value": ""}, {"alias": "五联单号", "control": "text", "isRange": false, "name": "five_bills_code", "required": false, "value": ""}, {"alias": "废物运输车牌号", "control": "chosen", "isRange": false, "name": "car_id", "required": false, "value": ""}, {"alias": "装置", "control": "chosen", "isRange": false, "name": "parent_org_id", "required": false, "value": ""}, {"alias": "申请人", "control": "text", "name": "user_name", "required": false, "value": ""}, {"alias": "申请类型", "control": "select", "isRange": false, "name": "apply_type", "required": false, "value": ""}, {"alias": "车卡", "control": "text", "isRange": false, "name": "card_name", "required": false, "value": ""}, {"alias": "所属公司", "control": "select", "name": "company_id", "required": false, "value": ""}, {"alias": "固废名称", "control": "text", "name": "waste_name", "required": false, "value": ""}, {"alias": "处置去向", "control": "select", "name": "is_sales", "required": false, "value": ""}], "idName": "transfer_id", "nodeIdName": "transfer_id"}, "setting": "", "template": "", "type": "standard"}